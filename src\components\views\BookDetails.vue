<template>
  <div class="min-h-screen bg-gradient-to-br from-surface-50 to-primary-50 p-4">
    <!-- Empty State Card -->
    <Card v-if="!book" class="max-w-md mx-auto mt-20 shadow-lg">
      <template #content>
        <div class="text-center p-8">
          <Avatar
            icon="pi pi-book"
            size="xlarge"
            class="mb-6 bg-primary-100 text-primary-600"
          />
          <h1 class="text-2xl font-bold text-surface-800 mb-3">No Book Selected</h1>
          <p class="text-surface-600 leading-relaxed">
            Please select a book from the sidebar to view its details and manage chapters.
          </p>
        </div>
      </template>
    </Card>

    <!-- Book Details Content -->
    <div v-else class="space-y-6">
      <!-- Header Card -->
      <Card class="shadow-lg">
        <template #content>
          <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
            <div class="flex items-center gap-4">
              <Avatar
                icon="pi pi-book"
                size="large"
                class="bg-gradient-to-br from-primary-500 to-purple-600 text-white"
              />
              <div>
                <h1 class="text-3xl font-bold text-surface-800 mb-2">{{ book.title }}</h1>
                <div class="flex items-center gap-2">
                  <Chip
                    label="Active Book"
                    icon="pi pi-check-circle"
                    class="bg-green-100 text-green-800 border-green-200"
                  />
                  <Chip
                    :label="`${book.chapters?.length || 0} Chapters`"
                    icon="pi pi-list"
                    class="bg-blue-100 text-blue-800 border-blue-200"
                  />
                </div>
              </div>
            </div>
            <div class="flex gap-3">
              <Button
                v-if="authStore.canDeleteBooks"
                label="Delete Book"
                icon="pi pi-trash"
                severity="danger"
                outlined
                @click="confirmDeleteBook"
                class="transition-all duration-200"
              />
              <Button
                v-if="selectedChapterId && authStore.canDeleteChapters"
                label="Delete Chapter"
                icon="pi pi-trash"
                severity="danger"
                @click="deleteSelectedChapter"
                class="transition-all duration-200"
              />
            </div>
          </div>
        </template>
      </Card>

      <!-- Main Content Grid -->
      <div class="grid grid-cols-1 xl:grid-cols-12 gap-6">
        <!-- Chapters & Items Panel -->
        <div class="xl:col-span-4">
          <Panel class="h-fit shadow-lg">
            <template #header>
              <div class="flex items-center gap-3 w-full">
                <Avatar
                  icon="pi pi-list"
                  size="small"
                  class="bg-primary-100 text-primary-600"
                />
                <span class="font-semibold text-lg">Chapters & Items</span>
                <div class="ml-auto">
                  <Button
                    v-if="authStore.canCreateChapters"
                    v-tooltip="'Add Chapter'"
                    icon="pi pi-plus"
                    size="small"
                    outlined
                    @click="openAddChapterDialog"
                    class="transition-all duration-200"
                  />
                </div>
              </div>
            </template>

            <!-- Empty State -->
            <div v-if="treeData.length === 0" class="text-center py-12">
              <Avatar
                icon="pi pi-book"
                size="xlarge"
                class="mb-6 bg-surface-100 text-surface-400"
              />
              <h3 class="text-lg font-semibold text-surface-700 mb-2">No chapters available</h3>
              <p class="text-surface-500 mb-6">Create your first chapter to get started</p>
              <Button
                v-if="authStore.canCreateChapters"
                label="Add Chapter"
                icon="pi pi-plus"
                @click="openAddChapterDialog"
                class="transition-all duration-200"
              />
            </div>

            <!-- Tree Component -->
            <Tree
              v-else
              :value="treeData"
              selectionMode="single"
              v-model:selectionKeys="selectedKey"
              v-model:expandedKeys="expandedKeys"
              @node-select="onNodeSelect"
              @node-unselect="onNodeUnselect"
              @node-contextmenu="onNodeContextMenu"
              class="border-0 p-0"
            />
          </Panel>
        </div>

        <!-- Main Content Area -->
        <div class="xl:col-span-8">
          <!-- QR Code Display Card -->
          <Card v-if="selectedItemId && !editingItemId" class="shadow-lg">
            <template #header>
              <div class="flex items-center gap-3 p-6 bg-gradient-to-r from-green-50 to-emerald-50 border-b">
                <Avatar
                  icon="pi pi-qrcode"
                  size="small"
                  class="bg-green-100 text-green-600"
                />
                <h2 class="text-xl font-bold text-surface-800">Item QR Code</h2>
              </div>
            </template>
            <template #content>
              <div class="p-6">
                <QRCodeDisplay
                  :bookId="book!.id"
                  :chapterId="selectedChapterId!"
                  :itemId="selectedItemId"
                  :onDelete="() => deleteSelectedItem()"
                  :onEdit="() => startEditingItem()"
                />
              </div>
            </template>
          </Card>

          <!-- Item Edit Form Card -->
          <Card v-else-if="editingItemId && selectedItem" class="shadow-lg">
            <template #header>
              <div class="flex items-center gap-3 p-6 bg-gradient-to-r from-orange-50 to-amber-50 border-b">
                <Avatar
                  icon="pi pi-pencil"
                  size="small"
                  class="bg-orange-100 text-orange-600"
                />
                <h2 class="text-xl font-bold text-surface-800">Edit Item</h2>
              </div>
            </template>
            <template #content>
              <div class="p-6">
                <ItemEditForm
                  :item="selectedItem"
                  :bookId="book!.id"
                  :chapterId="selectedChapterId!"
                  :onSave="handleItemSaved"
                  :onCancel="cancelEditingItem"
                />
              </div>
            </template>
          </Card>

          <!-- Item Creation Form Card -->
          <Card v-else-if="selectedChapterId" class="shadow-lg">
            <template #header>
              <div class="flex items-center gap-3 p-6 bg-gradient-to-r from-purple-50 to-indigo-50 border-b">
                <Avatar
                  icon="pi pi-plus"
                  size="small"
                  class="bg-purple-100 text-purple-600"
                />
                <h2 class="text-xl font-bold text-surface-800">Create New Item</h2>
              </div>
            </template>
            <template #content>
              <div class="p-6">
                <ItemForm
                  v-if="authStore.canEdit"
                  :chapterId="selectedChapterId"
                  :onItemCreated="refreshTreeData"
                />
                <div v-else class="text-center py-12">
                  <Avatar
                    icon="pi pi-lock"
                    size="xlarge"
                    class="mb-6 bg-red-100 text-red-500"
                  />
                  <h3 class="text-lg font-semibold text-surface-700 mb-2">Access Restricted</h3>
                  <p class="text-surface-500">You don't have permission to create or edit items</p>
                </div>
              </div>
            </template>
          </Card>

          <!-- Default State Card -->
          <Card v-else class="shadow-lg">
            <template #content>
              <div class="text-center py-16">
                <Avatar
                  icon="pi pi-arrow-left"
                  size="xlarge"
                  class="mb-6 bg-surface-100 text-surface-400"
                />
                <h3 class="text-xl font-semibold text-surface-700 mb-2">Get Started</h3>
                <p class="text-surface-500">Select a chapter or item from the sidebar to begin working</p>
              </div>
            </template>
          </Card>
        </div>
      </div>
    </div>
      <Dialog v-model:visible="showAddChapterDialog" header="Add Chapter" :style="{ width: '25vw' }">
        <InputText v-model="newChapterTitle" placeholder="Chapter Title" class="w-full" />
        <template #footer>
          <Button label="Cancel" class="p-button-text" @click="showAddChapterDialog = false" />
          <Button label="Save" @click="saveChapter" />
        </template>
      </Dialog>

      <!-- Delete Confirmation Dialog -->
      <Dialog v-model:visible="showDeleteDialog" header="Confirm Delete" :style="{ width: '30vw' }">
        <p class="mb-4">{{ deleteMessage }}</p>
        <template #footer>
          <Button label="Cancel" class="p-button-text" @click="showDeleteDialog = false" />
          <Button label="Delete" severity="danger" @click="performDelete" />
        </template>
      </Dialog>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useMainStore } from '../../stores/main';
import { useAuthStore } from '../../stores/auth';
import type { Book } from '../../types/book';
import type { TreeNode } from 'primevue/treenode';
import ItemForm from '../common/ItemForm.vue';
import ItemEditForm from '../common/ItemEditForm.vue';
import QRCodeDisplay from '../common/QRCodeDisplay.vue';
import Dialog from 'primevue/dialog';
import Button from 'primevue/button';
import Tree from 'primevue/tree';
import InputText from 'primevue/inputtext';
import Card from 'primevue/card';
import Panel from 'primevue/panel';
import Avatar from 'primevue/avatar';
import Chip from 'primevue/chip';
import { NodeService } from '../../service/NodeService';
import type { TreeSelectionKeys } from 'primevue/tree';

const store = useMainStore();
const authStore = useAuthStore();
const route = useRoute();
const router = useRouter();
const book = computed<Book | undefined>(() => store.getBook(route.params.id as string));
const selectedItem = computed(() => {
  if (!selectedItemId.value || !book.value) return null;
  const chapter = book.value.chapters.find(c => c.id === selectedChapterId.value);
  return chapter?.items.find(i => i.id === selectedItemId.value) || null;
});
const selectedKey = ref<TreeSelectionKeys | undefined>(undefined);
const expandedKeys = ref<TreeSelectionKeys>({});
const selectedChapterId = ref<string | null>(null);
const selectedItemId = ref<string | null>(null);
const editingItemId = ref<string | null>(null);
const showAddChapterDialog = ref(false);
const newChapterTitle = ref('');
const treeData = ref<TreeNode[]>([]);

// Delete functionality
const showDeleteDialog = ref(false);
const deleteMessage = ref('');
const nodeToDelete = ref<TreeNode | null>(null);

// Function to expand all tree nodes
const expandAllNodes = (nodes: TreeNode[]): TreeSelectionKeys => {
  const expanded: TreeSelectionKeys = {};

  const expandNode = (node: TreeNode) => {
    if (node.children && node.children.length > 0) {
      expanded[node.key!] = true;
      node.children.forEach(expandNode);
    }
  };

  nodes.forEach(expandNode);
  return expanded;
};

// Function to refresh tree data
const refreshTreeData = async () => {
  if (book.value) {
    const currentSelectedKey = selectedKey.value;
    const currentChapterId = selectedChapterId.value;
    const currentItemId = selectedItemId.value;

    treeData.value = await NodeService.getTreeNodes(book.value);

    // Expand all nodes to keep tree expanded
    expandedKeys.value = expandAllNodes(treeData.value);

    // Restore selection if it still exists
    if (currentItemId && book.value.chapters.some(ch => ch.items.some(item => item.id === currentItemId))) {
      selectedKey.value = currentSelectedKey;
      selectedChapterId.value = currentChapterId;
      selectedItemId.value = currentItemId;
    } else if (currentChapterId && book.value.chapters.some(ch => ch.id === currentChapterId)) {
      selectedKey.value = currentSelectedKey;
      selectedChapterId.value = currentChapterId;
      selectedItemId.value = null;
    } else if (currentSelectedKey) {
      // If neither item nor chapter exists anymore, clear selection
      clearSelection();
    }
  }
};

// Helper function to clear all selections
const clearSelection = () => {
  selectedKey.value = undefined;
  selectedChapterId.value = null;
  selectedItemId.value = null;
  editingItemId.value = null; // Also clear editing state
};

// Load tree data when book changes
watch(
  book,
  async (newBook) => {
    if (newBook) {
      treeData.value = await NodeService.getTreeNodes(newBook);
      // Expand all nodes when initially loading
      expandedKeys.value = expandAllNodes(treeData.value);
    } else {
      treeData.value = [];
      expandedKeys.value = {};
    }

    // Clear selections when book changes to prevent 404 errors
    clearSelection();
  },
  { immediate: true }
);

// Watch for selection key changes to handle deselection
watch(
  selectedKey,
  (newSelectedKey) => {
    // If selectedKey becomes empty or undefined, clear all selections
    if (!newSelectedKey || Object.keys(newSelectedKey).length === 0) {
      selectedChapterId.value = null;
      selectedItemId.value = null;
      editingItemId.value = null;
    }
  }
);

const onNodeSelect = (node: TreeNode) => {
  if (!book.value || !node.key) {
    clearSelection();
    return;
  }

  selectedKey.value = { [node.key]: { checked: true } };

  if (node.children) {
    // Chapter (has children)
    selectedChapterId.value = node.key;
    selectedItemId.value = null;
  } else {
    // Item (no children)
    const chapter = book.value.chapters.find(ch =>
      ch.items.some(item => item.id === node.key)
    );
    selectedChapterId.value = chapter?.id ?? null;
    selectedItemId.value = node.key;
  }
};

const onNodeUnselect = () => {
  clearSelection();
};

const openAddChapterDialog = () => {
  newChapterTitle.value = '';
  showAddChapterDialog.value = true;
};

const saveChapter = async () => {
  if (book.value && newChapterTitle.value) {
    await store.addChapter(book.value.id, { title: newChapterTitle.value });
    showAddChapterDialog.value = false;
    // Refresh tree data after adding chapter
    await refreshTreeData();
  }
};

// Item editing functions
const startEditingItem = () => {
  editingItemId.value = selectedItemId.value;
};

const cancelEditingItem = () => {
  editingItemId.value = null;
};

const handleItemSaved = async (updatedItem: any) => {
  editingItemId.value = null;
  // Refresh tree data to show updated item
  await refreshTreeData();
  // Maintain selection after update
  if (updatedItem && selectedItemId.value === updatedItem.id) {
    selectedKey.value = { [updatedItem.id]: { checked: true } };
  }
};

// Delete functionality
const onNodeContextMenu = (event: any) => {
  // Context menu functionality can be added here if needed
  console.log('Context menu for node:', event.node);
};



const confirmDeleteBook = () => {
  if (!book.value) return;

  nodeToDelete.value = {
    key: book.value.id,
    label: book.value.title,
    data: 'book'
  } as TreeNode;

  deleteMessage.value = `Are you sure you want to delete the book "${book.value.title}" and all its chapters and items? This action cannot be undone.`;
  showDeleteDialog.value = true;
};

const deleteSelectedItem = () => {
  if (!selectedItemId.value || !book.value) return;

  // Find the item to get its title
  const chapter = book.value.chapters.find(ch =>
    ch.items.some(item => item.id === selectedItemId.value)
  );
  const item = chapter?.items.find(item => item.id === selectedItemId.value);

  if (!item) return;

  nodeToDelete.value = {
    key: selectedItemId.value,
    label: item.title,
    data: 'item'
  } as TreeNode;

  deleteMessage.value = `Are you sure you want to delete the item "${item.title}"?`;
  showDeleteDialog.value = true;
};

const deleteSelectedChapter = () => {
  if (!selectedChapterId.value || !book.value) return;

  // Find the chapter to get its title
  const chapter = book.value.chapters.find(ch => ch.id === selectedChapterId.value);

  if (!chapter) return;

  nodeToDelete.value = {
    key: selectedChapterId.value,
    label: chapter.title,
    data: 'chapter',
    children: chapter.items.length > 0 ? [] : undefined // Indicate if it has items
  } as TreeNode;

  if (chapter.items.length > 0) {
    deleteMessage.value = `Are you sure you want to delete the chapter "${chapter.title}" and all its ${chapter.items.length} items? This action cannot be undone.`;
  } else {
    deleteMessage.value = `Are you sure you want to delete the chapter "${chapter.title}"?`;
  }

  showDeleteDialog.value = true;
};

const performDelete = async () => {
  if (!nodeToDelete.value) {
    return;
  }

  const node = nodeToDelete.value;

  try {
    if (node.data === 'book') {
      // It's a book
      await store.deleteBook(node.key);
      // Navigate to home since the book is deleted
      router.push('/');
    } else if (node.data === 'item' || (!node.children && node.data !== 'book')) {
      // It's an item (either explicitly marked or inferred from lack of children)
      if (!book.value) return;
      const chapter = book.value.chapters.find(ch =>
        ch.items.some(item => item.id === node.key)
      );

      if (chapter) {
        await store.deleteItem(book.value.id, chapter.id, node.key);

        // Clear selection if deleted item was selected
        if (selectedItemId.value === node.key) {
          clearSelection();
        }
      }
    } else if (node.data === 'chapter' || node.children !== undefined) {
      // It's a chapter
      if (!book.value) return;
      await store.deleteChapter(book.value.id, node.key);

      // Clear selection if deleted chapter was selected
      if (selectedChapterId.value === node.key) {
        clearSelection();
      }
    }

    showDeleteDialog.value = false;
    nodeToDelete.value = null;

    // Refresh tree data after deletion (except for book deletion since we navigate away)
    if (node.data !== 'book') {
      await refreshTreeData();
    }
  } catch (error) {
    console.error('Error deleting:', error);
    // You could show an error message here
  }
};
</script>