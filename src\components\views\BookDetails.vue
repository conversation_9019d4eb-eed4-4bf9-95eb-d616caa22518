<template>
  <div class="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
    <!-- Empty State -->
    <div v-if="!book" class="flex items-center justify-center min-h-[60vh]">
      <div class="text-center p-12 bg-white rounded-2xl shadow-lg border border-slate-200 max-w-md mx-auto">
        <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
          <i class="pi pi-book text-2xl text-blue-600"></i>
        </div>
        <h1 class="text-2xl font-bold text-slate-800 mb-3">No Book Selected</h1>
        <p class="text-slate-600 leading-relaxed">Please select a book from the sidebar to view its details and manage chapters.</p>
      </div>
    </div>

    <!-- Book Details Content -->
    <div v-else class="p-6 space-y-8">
      <!-- Header Section -->
      <div class="bg-white rounded-2xl shadow-lg border border-slate-200 p-8">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div class="flex items-center gap-4">
            <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
              <i class="pi pi-book text-white text-xl"></i>
            </div>
            <div>
              <h1 class="text-3xl font-bold text-slate-800 mb-1">{{ book.title }}</h1>
              <p class="text-slate-600">Manage chapters and learning items</p>
            </div>
          </div>
          <div class="flex gap-3">
            <Button
              v-if="authStore.canDeleteBooks"
              label="Delete Book"
              icon="pi pi-trash"
              severity="danger"
              outlined
              @click="confirmDeleteBook"
              class="transition-all duration-200 hover:scale-105"
            />
            <Button
              v-if="selectedChapterId && authStore.canDeleteChapters"
              label="Delete Chapter"
              icon="pi pi-trash"
              severity="danger"
              @click="deleteSelectedChapter"
              class="transition-all duration-200 hover:scale-105"
            />
          </div>
        </div>
      </div>

      <!-- Main Content Grid -->
      <div class="grid grid-cols-1 xl:grid-cols-12 gap-8">
        <!-- Chapters & Items Sidebar -->
        <div class="xl:col-span-4">
          <div class="bg-white rounded-2xl shadow-lg border border-slate-200 overflow-hidden">
            <!-- Sidebar Header -->
            <div class="bg-gradient-to-r from-slate-50 to-blue-50 p-6 border-b border-slate-200">
              <div class="flex items-center justify-between">
                <div class="flex items-center gap-3">
                  <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                    <i class="pi pi-list text-blue-600"></i>
                  </div>
                  <h2 class="text-xl font-bold text-slate-800">Chapters & Items</h2>
                </div>
                <Button
                  v-if="authStore.canCreateChapters"
                  v-tooltip="'Add Chapter'"
                  size="small"
                  icon="pi pi-plus"
                  outlined
                  @click="openAddChapterDialog"
                  class="transition-all duration-200 hover:scale-105"
                />
              </div>
            </div>

            <!-- Tree Content -->
            <div class="p-6">
              <div v-if="treeData.length === 0" class="text-center py-12">
                <div class="w-16 h-16 bg-slate-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <i class="pi pi-book text-2xl text-slate-400"></i>
                </div>
                <p class="text-lg font-medium text-slate-700 mb-2">No chapters available</p>
                <p class="text-sm text-slate-500 mb-6">Create your first chapter to get started</p>
                <Button
                  v-if="authStore.canCreateChapters"
                  label="Add Chapter"
                  icon="pi pi-plus"
                  @click="openAddChapterDialog"
                  class="transition-all duration-200 hover:scale-105"
                />
              </div>
              <Tree
                v-else
                :value="treeData"
                selectionMode="single"
                v-model:selectionKeys="selectedKey"
                v-model:expandedKeys="expandedKeys"
                @node-select="onNodeSelect"
                @node-unselect="onNodeUnselect"
                @node-contextmenu="onNodeContextMenu"
                class="custom-tree"
              />
            </div>
          </div>
        </div>

        <!-- Main Content Area -->
        <div class="xl:col-span-8">
          <!-- QR Code Display -->
          <div v-if="selectedItemId && !editingItemId" class="bg-white rounded-2xl shadow-lg border border-slate-200 overflow-hidden">
            <div class="bg-gradient-to-r from-green-50 to-emerald-50 p-6 border-b border-slate-200">
              <div class="flex items-center gap-3">
                <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                  <i class="pi pi-qrcode text-green-600"></i>
                </div>
                <h2 class="text-xl font-bold text-slate-800">Item QR Code</h2>
              </div>
            </div>
            <div class="p-8">
              <QRCodeDisplay
                :bookId="book!.id"
                :chapterId="selectedChapterId!"
                :itemId="selectedItemId"
                :onDelete="() => deleteSelectedItem()"
                :onEdit="() => startEditingItem()"
              />
            </div>
          </div>

          <!-- Item Edit Form -->
          <div v-else-if="editingItemId && selectedItem" class="bg-white rounded-2xl shadow-lg border border-slate-200 overflow-hidden">
            <div class="bg-gradient-to-r from-orange-50 to-amber-50 p-6 border-b border-slate-200">
              <div class="flex items-center gap-3">
                <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
                  <i class="pi pi-pencil text-orange-600"></i>
                </div>
                <h2 class="text-xl font-bold text-slate-800">Edit Item</h2>
              </div>
            </div>
            <div class="p-8">
              <ItemEditForm
                :item="selectedItem"
                :bookId="book!.id"
                :chapterId="selectedChapterId!"
                :onSave="handleItemSaved"
                :onCancel="cancelEditingItem"
              />
            </div>
          </div>

          <!-- Item Creation Form -->
          <div v-else-if="selectedChapterId" class="bg-white rounded-2xl shadow-lg border border-slate-200 overflow-hidden">
            <div class="bg-gradient-to-r from-purple-50 to-indigo-50 p-6 border-b border-slate-200">
              <div class="flex items-center gap-3">
                <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                  <i class="pi pi-plus text-purple-600"></i>
                </div>
                <h2 class="text-xl font-bold text-slate-800">Create New Item</h2>
              </div>
            </div>
            <div class="p-8">
              <ItemForm
                v-if="authStore.canEdit"
                :chapterId="selectedChapterId"
                :onItemCreated="refreshTreeData"
              />
              <div v-else class="text-center py-12">
                <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <i class="pi pi-lock text-2xl text-red-500"></i>
                </div>
                <p class="text-lg font-medium text-slate-700 mb-2">Access Restricted</p>
                <p class="text-sm text-slate-500">You don't have permission to create or edit items</p>
              </div>
            </div>
          </div>

          <!-- Default State -->
          <div v-else class="bg-white rounded-2xl shadow-lg border border-slate-200 p-12 text-center">
            <div class="w-16 h-16 bg-slate-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <i class="pi pi-arrow-left text-2xl text-slate-400"></i>
            </div>
            <p class="text-lg text-slate-600">Select a chapter or item from the sidebar to get started</p>
          </div>
        </div>
      </div>
    </div>
      <Dialog v-model:visible="showAddChapterDialog" header="Add Chapter" :style="{ width: '25vw' }">
        <InputText v-model="newChapterTitle" placeholder="Chapter Title" class="w-full" />
        <template #footer>
          <Button label="Cancel" class="p-button-text" @click="showAddChapterDialog = false" />
          <Button label="Save" @click="saveChapter" />
        </template>
      </Dialog>

      <!-- Delete Confirmation Dialog -->
      <Dialog v-model:visible="showDeleteDialog" header="Confirm Delete" :style="{ width: '30vw' }">
        <p class="mb-4">{{ deleteMessage }}</p>
        <template #footer>
          <Button label="Cancel" class="p-button-text" @click="showDeleteDialog = false" />
          <Button label="Delete" severity="danger" @click="performDelete" />
        </template>
      </Dialog>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useMainStore } from '../../stores/main';
import { useAuthStore } from '../../stores/auth';
import type { Book } from '../../types/book';
import type { TreeNode } from 'primevue/treenode';
import ItemForm from '../common/ItemForm.vue';
import ItemEditForm from '../common/ItemEditForm.vue';
import QRCodeDisplay from '../common/QRCodeDisplay.vue';
import Dialog from 'primevue/dialog';
import Button from 'primevue/button';
import Tree from 'primevue/tree';
import InputText from 'primevue/inputtext';
import { NodeService } from '../../service/NodeService';
import type { TreeSelectionKeys } from 'primevue/tree';

const store = useMainStore();
const authStore = useAuthStore();
const route = useRoute();
const router = useRouter();
const book = computed<Book | undefined>(() => store.getBook(route.params.id as string));
const selectedItem = computed(() => {
  if (!selectedItemId.value || !book.value) return null;
  const chapter = book.value.chapters.find(c => c.id === selectedChapterId.value);
  return chapter?.items.find(i => i.id === selectedItemId.value) || null;
});
const selectedKey = ref<TreeSelectionKeys | undefined>(undefined);
const expandedKeys = ref<TreeSelectionKeys>({});
const selectedChapterId = ref<string | null>(null);
const selectedItemId = ref<string | null>(null);
const editingItemId = ref<string | null>(null);
const showAddChapterDialog = ref(false);
const newChapterTitle = ref('');
const treeData = ref<TreeNode[]>([]);

// Delete functionality
const showDeleteDialog = ref(false);
const deleteMessage = ref('');
const nodeToDelete = ref<TreeNode | null>(null);

// Function to expand all tree nodes
const expandAllNodes = (nodes: TreeNode[]): TreeSelectionKeys => {
  const expanded: TreeSelectionKeys = {};

  const expandNode = (node: TreeNode) => {
    if (node.children && node.children.length > 0) {
      expanded[node.key!] = true;
      node.children.forEach(expandNode);
    }
  };

  nodes.forEach(expandNode);
  return expanded;
};

// Function to refresh tree data
const refreshTreeData = async () => {
  if (book.value) {
    const currentSelectedKey = selectedKey.value;
    const currentChapterId = selectedChapterId.value;
    const currentItemId = selectedItemId.value;

    treeData.value = await NodeService.getTreeNodes(book.value);

    // Expand all nodes to keep tree expanded
    expandedKeys.value = expandAllNodes(treeData.value);

    // Restore selection if it still exists
    if (currentItemId && book.value.chapters.some(ch => ch.items.some(item => item.id === currentItemId))) {
      selectedKey.value = currentSelectedKey;
      selectedChapterId.value = currentChapterId;
      selectedItemId.value = currentItemId;
    } else if (currentChapterId && book.value.chapters.some(ch => ch.id === currentChapterId)) {
      selectedKey.value = currentSelectedKey;
      selectedChapterId.value = currentChapterId;
      selectedItemId.value = null;
    } else if (currentSelectedKey) {
      // If neither item nor chapter exists anymore, clear selection
      clearSelection();
    }
  }
};

// Helper function to clear all selections
const clearSelection = () => {
  selectedKey.value = undefined;
  selectedChapterId.value = null;
  selectedItemId.value = null;
  editingItemId.value = null; // Also clear editing state
};

// Load tree data when book changes
watch(
  book,
  async (newBook) => {
    if (newBook) {
      treeData.value = await NodeService.getTreeNodes(newBook);
      // Expand all nodes when initially loading
      expandedKeys.value = expandAllNodes(treeData.value);
    } else {
      treeData.value = [];
      expandedKeys.value = {};
    }

    // Clear selections when book changes to prevent 404 errors
    clearSelection();
  },
  { immediate: true }
);

// Watch for selection key changes to handle deselection
watch(
  selectedKey,
  (newSelectedKey) => {
    // If selectedKey becomes empty or undefined, clear all selections
    if (!newSelectedKey || Object.keys(newSelectedKey).length === 0) {
      selectedChapterId.value = null;
      selectedItemId.value = null;
      editingItemId.value = null;
    }
  }
);

const onNodeSelect = (node: TreeNode) => {
  if (!book.value || !node.key) {
    clearSelection();
    return;
  }

  selectedKey.value = { [node.key]: { checked: true } };

  if (node.children) {
    // Chapter (has children)
    selectedChapterId.value = node.key;
    selectedItemId.value = null;
  } else {
    // Item (no children)
    const chapter = book.value.chapters.find(ch =>
      ch.items.some(item => item.id === node.key)
    );
    selectedChapterId.value = chapter?.id ?? null;
    selectedItemId.value = node.key;
  }
};

const onNodeUnselect = () => {
  clearSelection();
};

const openAddChapterDialog = () => {
  newChapterTitle.value = '';
  showAddChapterDialog.value = true;
};

const saveChapter = async () => {
  if (book.value && newChapterTitle.value) {
    await store.addChapter(book.value.id, { title: newChapterTitle.value });
    showAddChapterDialog.value = false;
    // Refresh tree data after adding chapter
    await refreshTreeData();
  }
};

// Item editing functions
const startEditingItem = () => {
  editingItemId.value = selectedItemId.value;
};

const cancelEditingItem = () => {
  editingItemId.value = null;
};

const handleItemSaved = async (updatedItem: any) => {
  editingItemId.value = null;
  // Refresh tree data to show updated item
  await refreshTreeData();
  // Maintain selection after update
  if (updatedItem && selectedItemId.value === updatedItem.id) {
    selectedKey.value = { [updatedItem.id]: { checked: true } };
  }
};

// Delete functionality
const onNodeContextMenu = (event: any) => {
  // Context menu functionality can be added here if needed
  console.log('Context menu for node:', event.node);
};



const confirmDeleteBook = () => {
  if (!book.value) return;

  nodeToDelete.value = {
    key: book.value.id,
    label: book.value.title,
    data: 'book'
  } as TreeNode;

  deleteMessage.value = `Are you sure you want to delete the book "${book.value.title}" and all its chapters and items? This action cannot be undone.`;
  showDeleteDialog.value = true;
};

const deleteSelectedItem = () => {
  if (!selectedItemId.value || !book.value) return;

  // Find the item to get its title
  const chapter = book.value.chapters.find(ch =>
    ch.items.some(item => item.id === selectedItemId.value)
  );
  const item = chapter?.items.find(item => item.id === selectedItemId.value);

  if (!item) return;

  nodeToDelete.value = {
    key: selectedItemId.value,
    label: item.title,
    data: 'item'
  } as TreeNode;

  deleteMessage.value = `Are you sure you want to delete the item "${item.title}"?`;
  showDeleteDialog.value = true;
};

const deleteSelectedChapter = () => {
  if (!selectedChapterId.value || !book.value) return;

  // Find the chapter to get its title
  const chapter = book.value.chapters.find(ch => ch.id === selectedChapterId.value);

  if (!chapter) return;

  nodeToDelete.value = {
    key: selectedChapterId.value,
    label: chapter.title,
    data: 'chapter',
    children: chapter.items.length > 0 ? [] : undefined // Indicate if it has items
  } as TreeNode;

  if (chapter.items.length > 0) {
    deleteMessage.value = `Are you sure you want to delete the chapter "${chapter.title}" and all its ${chapter.items.length} items? This action cannot be undone.`;
  } else {
    deleteMessage.value = `Are you sure you want to delete the chapter "${chapter.title}"?`;
  }

  showDeleteDialog.value = true;
};

const performDelete = async () => {
  if (!nodeToDelete.value) {
    return;
  }

  const node = nodeToDelete.value;

  try {
    if (node.data === 'book') {
      // It's a book
      await store.deleteBook(node.key);
      // Navigate to home since the book is deleted
      router.push('/');
    } else if (node.data === 'item' || (!node.children && node.data !== 'book')) {
      // It's an item (either explicitly marked or inferred from lack of children)
      if (!book.value) return;
      const chapter = book.value.chapters.find(ch =>
        ch.items.some(item => item.id === node.key)
      );

      if (chapter) {
        await store.deleteItem(book.value.id, chapter.id, node.key);

        // Clear selection if deleted item was selected
        if (selectedItemId.value === node.key) {
          clearSelection();
        }
      }
    } else if (node.data === 'chapter' || node.children !== undefined) {
      // It's a chapter
      if (!book.value) return;
      await store.deleteChapter(book.value.id, node.key);

      // Clear selection if deleted chapter was selected
      if (selectedChapterId.value === node.key) {
        clearSelection();
      }
    }

    showDeleteDialog.value = false;
    nodeToDelete.value = null;

    // Refresh tree data after deletion (except for book deletion since we navigate away)
    if (node.data !== 'book') {
      await refreshTreeData();
    }
  } catch (error) {
    console.error('Error deleting:', error);
    // You could show an error message here
  }
};
</script>